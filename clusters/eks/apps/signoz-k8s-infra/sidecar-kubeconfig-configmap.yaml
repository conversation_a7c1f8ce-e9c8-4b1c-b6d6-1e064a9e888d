# ConfigMap with in-cluster kubeconfig for API-server proxy access
# This enables the sidecar collector to access kubelet stats via API-server proxy
# Required for EKS Fargate where direct kubelet access is not available
apiVersion: v1
kind: ConfigMap
metadata:
  name: otel-agent-sidecar-kubeconfig
  namespace: observability
data:
  # minimal kubeconfig so the collector can talk to the API server via the pod SA
  kubeconfig: |
    apiVersion: v1
    kind: Config
    clusters:
    - name: in-cluster
      cluster:
        server: https://kubernetes.default.svc:443
        certificate-authority: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    contexts:
    - name: in-cluster
      context: { cluster: in-cluster, user: in-cluster }
    current-context: in-cluster
    users:
    - name: in-cluster
      user:
        tokenFile: /var/run/secrets/kubernetes.io/serviceaccount/token
