# ConfigMap with OpenTelemetry Collector configuration for sidecar
# This configuration is optimized for EKS Fargate serverless environments
apiVersion: v1
kind: ConfigMap
metadata:
  name: otel-agent-sidecar-config
  namespace: observability
data:
  otel-agent-config.yaml: |
    exporters:
      debug:
        sampling_initial: 2
        sampling_thereafter: 500
        verbosity: basic
      otlp:
        endpoint: ${env:OTEL_EXPORTER_OTLP_ENDPOINT}
        headers:
          signoz-ingestion-key: ${env:SIGNOZ_API_KEY}
        tls:
          insecure: ${env:OTEL_EXPORTER_OTLP_INSECURE}
          insecure_skip_verify: ${env:OTEL_EXPORTER_OTLP_INSECURE_SKIP_VERIFY}

    extensions:
      health_check:
        endpoint: 0.0.0.0:13133
      pprof:
        endpoint: localhost:1777
      zpages:
        endpoint: localhost:55679
    processors:
      batch:
        send_batch_size: 10000
        timeout: 200ms
      k8sattributes:
        extract:
          annotations: []
          labels: []
          metadata:
            - k8s.namespace.name
            - k8s.deployment.name
            - k8s.statefulset.name
            - k8s.daemonset.name
            - k8s.cronjob.name
            - k8s.job.name
            - k8s.node.name
            - k8s.node.uid
            - k8s.pod.name
            - k8s.pod.uid
            - k8s.pod.start_time
        filter:
          node_from_env_var: K8S_NODE_NAME
        passthrough: false
        pod_association:
        - sources:
          - from: resource_attribute
            name: k8s.pod.ip
        - sources:
          - from: resource_attribute
            name: k8s.pod.uid
        - sources:
          - from: connection
      resourcedetection:
        detectors:
        - eks
        - env
        - system
        override: false
        system:
          resource_attributes:
            host.id:
              enabled: false
            host.name:
              enabled: false
            os.type:
              enabled: true
        timeout: 2s

    receivers:
      kubeletstats:
        auth_type: kubeConfig
        collection_interval: 30s
        endpoint: ${env:K8S_NODE_NAME}
        insecure_skip_verify: true
        metric_groups:
        - container
        - pod
        - node
        - volume
        metrics:
          container.cpu.usage:
            enabled: true
          container.uptime:
            enabled: true
          k8s.container.cpu_limit_utilization:
            enabled: true
          k8s.container.cpu_request_utilization:
            enabled: true
          k8s.container.memory_limit_utilization:
            enabled: true
          k8s.container.memory_request_utilization:
            enabled: true
          k8s.node.cpu.usage:
            enabled: true
          k8s.node.uptime:
            enabled: true
          k8s.pod.cpu.usage:
            enabled: true
          k8s.pod.cpu_limit_utilization:
            enabled: true
          k8s.pod.cpu_request_utilization:
            enabled: true
          k8s.pod.memory_limit_utilization:
            enabled: true
          k8s.pod.memory_request_utilization:
            enabled: true
          k8s.pod.uptime:
            enabled: true
      otlp:
        protocols:
          grpc: { endpoint: 0.0.0.0:4317, max_recv_msg_size_mib: 4 }
          http: { endpoint: 0.0.0.0:4318 }

    service:
      extensions: [health_check, zpages, pprof]
      pipelines:
        logs:
          receivers:  [otlp]
          processors: [resourcedetection, k8sattributes, batch]
          exporters:  [debug, otlp]
        metrics:
          receivers:  [otlp, kubeletstats]
          processors: [resourcedetection, k8sattributes, batch]
          exporters:  [otlp, debug]
        traces:
          receivers:  [otlp]
          processors: [resourcedetection, k8sattributes, batch]
          exporters:  [otlp, debug]

      telemetry:
        logs:    { encoding: json }
        metrics: { address: 0.0.0.0:8888 }
