# Example Deployment showing how to add OpenTelemetry sidecar collector
# This demonstrates the integration pattern for EKS Fargate applications
# Replace placeholders with your actual values:
# - <YOUR_APP_IMAGE>: Your application container image
# - <OTLP_EXPORTER_OTLP_ENDPOINT>: Your SigNoz endpoint
# - <YOUR_K8S_CLUSTER_NAME>: Your cluster name
# - <YOUR_NAMESPACE>: Your application namespace

apiVersion: apps/v1
kind: Deployment
metadata:
  name: example-app-with-otel-sidecar
  namespace: <YOUR_NAMESPACE>
spec:
  replicas: 1
  selector:
    matchLabels:
      app: example-app
  template:
    metadata:
      labels:
        app: example-app
    spec:
      ## Service Account for otel-agent-sidecar container ##
      serviceAccountName: otel-agent-sidecar

      ## Mount the otel-agent config configMap
      volumes:
        - name: otel-agent-config-vol
          configMap:
            name: otel-agent-sidecar-config
        - name: varlog
          emptyDir: {}

      containers:
        # Your application container
        - name: your-app
          image: <YOUR_APP_IMAGE>
          env:
            # Configure your app to send telemetry to the sidecar collector
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: http://localhost:4317
          ports:
            - containerPort: 8080
              name: http

        ###### OpenTelemetry Agent Sidecar container ####
        - name: opentelemetry-collector-contrib
          image: otel/opentelemetry-collector-contrib:0.109.0
          command:
            - /otelcol-contrib
            - --config=/conf/otel-agent-config.yaml
          env:
            # This endpoint should be reachable from your pod (your SigNoz backend)
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: <OTLP_EXPORTER_OTLP_ENDPOINT>
            - name: OTEL_EXPORTER_OTLP_INSECURE
              value: "true"
            - name: OTEL_EXPORTER_OTLP_INSECURE_SKIP_VERIFY
              value: "false"
            - name: SIGNOZ_API_KEY
              value: ""  # Add your SigNoz API key if required
            - name: OTEL_SECRETS_PATH
              value: /secrets
            - name: K8S_CLUSTER_NAME
              value: <YOUR_K8S_CLUSTER_NAME>
            - name: K8S_NODE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            - name: K8S_POD_IP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
            - name: K8S_HOST_IP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: K8S_POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: K8S_POD_UID
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.uid
            - name: K8S_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
            - name: SIGNOZ_COMPONENT
              value: otel-agent
            - name: OTEL_RESOURCE_ATTRIBUTES
              value: signoz.component=$(SIGNOZ_COMPONENT),k8s.cluster.name=$(K8S_CLUSTER_NAME),k8s.node.name=$(K8S_NODE_NAME),host.name=$(K8S_NODE_NAME)
          livenessProbe:
            failureThreshold: 6
            httpGet:
              path: /
              port: 13133
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          ports:
            - containerPort: 13133
              name: health-check
              protocol: TCP
            - containerPort: 8888
              name: metrics
              protocol: TCP
            - containerPort: 4317
              name: otlp
              protocol: TCP
            - containerPort: 4318
              name: otlp-http
              protocol: TCP
          readinessProbe:
            failureThreshold: 6
            httpGet:
              path: /
              port: 13133
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            requests:
              cpu: 100m
              memory: 100Mi
          volumeMounts:
            - mountPath: /conf
              name: otel-agent-config-vol
            - mountPath: /var/log
              name: varlog
              readOnly: true
