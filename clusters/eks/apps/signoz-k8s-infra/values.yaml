k8s-infra:
  global:
    storageClass: "gp3"
    cloud: aws

  presets:
    otlpExporter:
      enabled: false
    otlphttpExporter:
      enabled: true
    logsCollection:
      enabled: true
      blacklist:
        enabled: true
        signozLogs: true
        namespaces:
          - kube-system
          - anduin-ai
          - infra-shared
          - logging
          - analytics
          - infosec
          - monitoring
          - tracing
          - keda
          - networking
          - observability
          - tidb-admin
          - vault
          - external-dns
          - cert-manager

  otelAgent:
    tolerations:
      - operator: Exists
        effect: NoSchedule
        key: spot-instance
    ports:
      otlp:
        hostPort: 4319
      otlp-http:
        hostPort: 4320
      health-check:
        hostPort: 13134
      metrics:
        hostPort: 8889

  otelDeployment:
    tolerations:
      - operator: Exists
        effect: NoSchedule
        key: spot-instance
