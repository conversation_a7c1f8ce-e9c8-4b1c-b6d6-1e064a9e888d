k8s-infra:
  global:
    storageClass: "gp3"
    cloud: aws

  presets:
    otlpExporter:
      enabled: false
    otlphttpExporter:
      enabled: true
    logsCollection:
      enabled: true
      whitelist:
        enabled: true
        containers:
          - gondor
          - ses
          - gondor-initializer
          - gondor-migration
          - gondor-hotfix-migration
      # Ref: https://github.com/open-telemetry/opentelemetry-collector-contrib/blob/main/pkg/stanza/docs/operators/README.md#what-operators-are-available
      operators:
        - id: container-parser
          type: container
        #        - id: json-parser
        #          type: json_parser
        #          from: body
        #        - id: trace-id-extractor
        #          type: move
        #          from: attributes.otel_trace_id
        #          to: trace_id
        #        - id: span-id-extractor
        #          type: move
        #          from: attributes.otel_span_id
        #          to: span_id
        #        - id: severity-text-id-extractor
        #          type: move
        #          from: attributes.otel_span_id
        #          to: span_id

  otelAgent:
    tolerations:
      - operator: Exists
        effect: NoSchedule
        key: spot-instance
    ports:
      otlp:
        hostPort: 4319
      otlp-http:
        hostPort: 4320
      health-check:
        hostPort: 13134
      metrics:
        hostPort: 8889

  otelDeployment:
    tolerations:
      - operator: Exists
        effect: NoSchedule
        key: spot-instance
