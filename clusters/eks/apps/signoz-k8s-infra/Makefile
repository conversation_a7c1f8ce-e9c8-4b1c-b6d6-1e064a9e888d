template-edoras:
	@helm template --release-name eks \
		--namespace observability \
		--values ./values.yaml \
		--values ./values.edoras.yaml \
		.

diff-edoras:
	make -s template-edoras | kubectl diff -n=observability -f -

apply-edoras:
	make -s template-edoras | kubectl apply -n=observability -f -

dryrun-edoras:
	make -s template-edoras | kubectl apply -n=observability -f - --dry-run=server

delete-edoras:
	make -s template-edoras | kubectl delete -n=observability -f - --dry-run=server

template-eustaging:
	@helm template --release-name eks \
		--namespace observability \
		--values ./values.yaml \
		--values ./values.eustaging.yaml \
		.

diff-eustaging:
	make -s template-eustaging | kubectl diff -n=observability -f -

apply-eustaging:
	make -s template-eustaging | kubectl apply -n=observability -f -

dryrun-eustaging:
	make -s template-eustaging | kubectl apply -n=observability -f - --dry-run=server

delete-eustaging:
	make -s template-eustaging | kubectl delete -n=observability -f - --dry-run=server


template-euprod:
	@helm template --release-name eks \
		--namespace observability \
		--values ./values.yaml \
		--values ./values.euprod.yaml \
		.

diff-euprod:
	make -s template-euprod | kubectl diff -n=observability -f -

apply-euprod:
	make -s template-euprod | kubectl apply -n=observability -f -

dryrun-euprod:
	make -s template-euprod | kubectl apply -n=observability -f - --dry-run=server

delete-euprod:
	make -s template-euprod | kubectl delete -n=observability -f - --dry-run=server

template-valinor:
	@helm template --release-name eks \
		--namespace observability \
		--values ./values.yaml \
		--values ./values.valinor.yaml \
		.

diff-valinor:
	make -s template-valinor | kubectl diff -n=observability -f -

apply-valinor:
	make -s template-valinor | kubectl apply -n=observability -f -

dryrun-valinor:
	make -s template-valinor | kubectl apply -n=observability -f - --dry-run=server

delete-valinor:
	make -s template-valinor | kubectl delete -n=observability -f - --dry-run=server
