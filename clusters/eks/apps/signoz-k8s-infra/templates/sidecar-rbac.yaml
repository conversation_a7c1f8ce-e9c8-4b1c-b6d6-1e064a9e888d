# RBAC resources for OpenTelemetry sidecar collector
# These permissions allow the sidecar to access kubelet stats via API-server proxy
# and perform resource enrichment through k8sattributes processor

---
# ServiceAccount for sidecar collector
apiVersion: v1
kind: ServiceAccount
metadata:
  name: otel-agent-sidecar
  namespace: observability

---
# ClusterRole with permissions for kubeletstats via API-server proxy
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: otel-kubeletstats-proxy
rules:
  # Needed for: GET /api/v1/nodes/<node>/proxy/stats/summary
  - apiGroups: [""]
    resources: ["nodes/stats", "nodes/proxy"]
    verbs: ["get"]
  # Informers for enrichment (k8sattributes/resourcedetection)
  - apiGroups: [""]
    resources: ["pods", "namespaces", "nodes"]
    verbs: ["get","list","watch"]
  # Optional: owner lookups (ReplicaSets)
  - apiGroups: ["apps"]
    resources: ["replicasets"]
    verbs: ["get","list","watch"]

---
# ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: otel-kubeletstats-proxy
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: otel-kubeletstats-proxy
subjects:
  - kind: ServiceAccount
    name: otel-agent-sidecar
    namespace: observability
