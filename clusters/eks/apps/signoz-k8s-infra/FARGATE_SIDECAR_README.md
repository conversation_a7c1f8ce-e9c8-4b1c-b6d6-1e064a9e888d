# SigNoz EKS Fargate Serverless Collection Agent

This directory contains Kubernetes manifests for setting up OpenTelemetry collection agents as sidecars in EKS Fargate environments, based on the [SigNoz documentation](https://signoz.io/docs/collection-agents/k8s/serverless/install/).

## Overview

On EKS Fargate, direct access to kubelet ports (:10250/:10255) isn't available. This setup uses the Kubernetes API-server proxy to scrape kubelet stats/summary via kubeletstats receiver.

## Files

1. **sidecar-kubeconfig-configmap.yaml** - ConfigMap with in-cluster kubeconfig for API-server proxy access
2. **sidecar-otel-config-configmap.yaml** - OpenTelemetry Collector configuration optimized for Fargate
3. **sidecar-rbac.yaml** - ServiceAccount, ClusterRole, and ClusterRoleBinding for required permissions
4. **example-app-with-sidecar.yaml** - Example deployment showing sidecar integration pattern

## Prerequisites

- An EKS cluster with Fargate profiles set up
- A SigNoz backend (either SigNoz Cloud or self-hosted)
- kubectl access to update your app Deployment/Pod specs
- The k8s-infra Helm chart installed (with otelAgent DaemonSet disabled for Fargate nodes)

## Installation Steps

### 1. Apply the base manifests

```bash
# Apply the kubeconfig ConfigMap
kubectl apply -f sidecar-kubeconfig-configmap.yaml

# Apply the OpenTelemetry collector configuration
kubectl apply -f sidecar-otel-config-configmap.yaml

# Apply RBAC resources
kubectl apply -f sidecar-rbac.yaml
```

### 2. Configure your application deployment

Use the `example-app-with-sidecar.yaml` as a template and replace the following placeholders:

- `<YOUR_APP_IMAGE>`: Your application container image
- `<OTLP_EXPORTER_OTLP_ENDPOINT>`: Your SigNoz endpoint (e.g., `http://signoz-otel-collector:4317`)
- `<YOUR_K8S_CLUSTER_NAME>`: Your cluster name
- `<YOUR_NAMESPACE>`: Your application namespace

### 3. Key configuration points

#### Environment Variables for Sidecar
The sidecar collector requires these environment variables:
- `OTEL_EXPORTER_OTLP_ENDPOINT`: SigNoz backend endpoint
- `SIGNOZ_API_KEY`: Your SigNoz API key (if required)
- `K8S_CLUSTER_NAME`: Your cluster identifier
- Various Kubernetes field references for pod metadata

#### Application Configuration
Configure your application to send telemetry to the sidecar:
```yaml
env:
  - name: OTEL_EXPORTER_OTLP_ENDPOINT
    value: http://localhost:4317  # Points to sidecar collector
```

#### Volume Mounts
The sidecar needs access to:
- `/conf` - OpenTelemetry configuration
- `/var/log` - Log files (if collecting logs)

## Troubleshooting

### 404 Not Found for /api/v1/nodes/proxy/stats/summary

Ensure:
1. The receiver endpoint is `endpoint: ${env:K8S_NODE_NAME}` (not empty/not host:port)
2. The env var is set via `spec.nodeName` and sidecar has `KUBECONFIG=/conf/kubeconfig`
3. RBAC allows `get` on `nodes/stats` and `nodes/proxy`

### Alternative for environments without /stats/summary

If your EKS Fargate environment doesn't expose `/stats/summary` via proxy, consider using cAdvisor via API-proxy method (`/api/v1/nodes/$NODE/proxy/metrics/cadvisor`) with a Prometheus receiver.

## Notes

- The sidecar acts as the per-pod agent (replacement for the DaemonSet otelAgent)
- Keep otelDeployment enabled in k8s-infra if you want cluster metrics/events
- The configuration uses kubeConfig auth_type for kubeletstats receiver
- Resource detection includes EKS-specific detectors
